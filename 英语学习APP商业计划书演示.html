<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语学习APP商业计划书 - 项老师AI工作室</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <script defer src="https://cdn.bootcdn.net/ajax/libs/alpinejs/3.12.3/cdn.min.js"></script>
    <style>
        /* AI风格设计 */
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            font-family: 'Microsoft YaHei', sans-serif;
            overflow: hidden;
        }

        /* 粒子背景 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: radial-gradient(circle, rgba(255,255,255,0.6) 0%, transparent 70%);
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(100vh) translateX(0px); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-10px) translateX(50px); opacity: 0; }
        }

        /* 玻璃透明效果 */
        .glass {
            background: linear-gradient(135deg, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.1) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1), 0 0 20px rgba(59,130,246,0.3);
        }

        /* 发光效果 */
        .glow {
            animation: pulse 3s infinite;
        }

        @keyframes pulse {
            0%, 100% { box-shadow: 0 0 20px rgba(59,130,246,0.4); }
            50% { box-shadow: 0 0 30px rgba(59,130,246,0.8); }
        }

        /* 幻灯片容器 */
        .slide-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            z-index: 10;
        }

        .slide {
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .slide.active {
            opacity: 1;
            transform: translateX(0);
        }

        .slide.prev {
            transform: translateX(-100%);
        }

        /* 导航按钮 */
        .nav-btn {
            position: fixed;
            top: 50%;
            transform: translateY(-50%);
            z-index: 20;
            background: rgba(59,130,246,0.3);
            border: 1px solid rgba(59,130,246,0.5);
            color: white;
            padding: 15px 20px;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(59,130,246,0.6);
            box-shadow: 0 0 20px rgba(59,130,246,0.8);
        }

        .nav-btn.prev { left: 30px; }
        .nav-btn.next { right: 30px; }

        /* 页面指示器 */
        .page-indicator {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 20;
            display: flex;
            gap: 10px;
        }

        .indicator-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .indicator-dot.active {
            background: #3b82f6;
            box-shadow: 0 0 10px rgba(59,130,246,0.8);
        }

        /* 页码显示 */
        .page-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            z-index: 20;
            color: rgba(255,255,255,0.8);
            font-size: 16px;
            background: rgba(0,0,0,0.3);
            padding: 10px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
        }

        /* 内容样式 */
        .slide-content {
            max-width: 1200px;
            width: 90%;
            padding: 40px;
            text-align: center;
        }

        .slide h1 {
            font-size: 3.5rem;
            font-weight: 100;
            color: white;
            margin-bottom: 30px;
            text-shadow: 0 0 20px rgba(59,130,246,0.5);
            letter-spacing: 2px;
        }

        .slide h2 {
            font-size: 2.5rem;
            font-weight: 300;
            color: #3b82f6;
            margin-bottom: 25px;
            text-shadow: 0 0 15px rgba(59,130,246,0.3);
        }

        .slide h3 {
            font-size: 1.8rem;
            font-weight: 400;
            color: #60a5fa;
            margin-bottom: 20px;
        }

        .slide p {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            line-height: 1.8;
            margin-bottom: 20px;
        }

        /* 特殊元素 */
        .highlight {
            color: #fbbf24;
            font-weight: 600;
            text-shadow: 0 0 10px rgba(251,191,36,0.5);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: rgba(255,255,255,0.1);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(59,130,246,0.3);
        }

        .data-table {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }

        .data-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .slide h1 { font-size: 2.5rem; }
            .slide h2 { font-size: 2rem; }
            .slide-content { padding: 20px; }
            .nav-btn { padding: 10px 15px; }
        }
    </style>
</head>
<body x-data="slideShow()">
    <!-- 粒子背景 -->
    <div class="particles" id="particles"></div>

    <!-- 页码计数器 -->
    <div class="page-counter">
        <span x-text="currentSlide + 1"></span> / <span x-text="totalSlides"></span>
    </div>

    <!-- 导航按钮 -->
    <button class="nav-btn prev" @click="prevSlide()" x-show="currentSlide > 0">
        <i class="fas fa-chevron-left"></i>
    </button>
    <button class="nav-btn next" @click="nextSlide()" x-show="currentSlide < totalSlides - 1">
        <i class="fas fa-chevron-right"></i>
    </button>

    <!-- 页面指示器 -->
    <div class="page-indicator">
        <template x-for="(slide, index) in slides" :key="index">
            <div class="indicator-dot" 
                 :class="{ 'active': index === currentSlide }"
                 @click="goToSlide(index)"></div>
        </template>
    </div>

    <!-- 幻灯片容器 -->
    <div class="slide-container">
        <!-- 第1页：封面 -->
        <div class="slide" :class="{ 'active': currentSlide === 0 }">
            <div class="slide-content glass glow">
                <div class="mb-8">
                    <img src="https://www.diwangzhidao.com/logo.png" alt="项老师logo" class="w-24 h-24 mx-auto mb-6 animate__animated animate__pulse animate__infinite">
                </div>
                <h1 class="animate__animated animate__fadeInDown">英语学习APP</h1>
                <h2 class="animate__animated animate__fadeInUp animate__delay-1s">商业计划书</h2>
                <p class="text-xl mt-8 animate__animated animate__fadeIn animate__delay-2s">
                    <span class="highlight">简化学习 · 快速提分 · 智能互动</span>
                </p>
                <p class="mt-4 animate__animated animate__fadeIn animate__delay-3s">
                    项老师AI工作室 · 2025年8月
                </p>
            </div>
        </div>

        <!-- 第2页：项目概述 -->
        <div class="slide" :class="{ 'active': currentSlide === 1 }">
            <div class="slide-content glass">
                <h2><i class="fas fa-rocket mr-4"></i>项目概述</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <i class="fas fa-mobile-alt text-3xl text-blue-400 mb-4"></i>
                        <h3>产品类型</h3>
                        <p>少儿英语学习APP<br>专注K12教育</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-brain text-3xl text-purple-400 mb-4"></i>
                        <h3>核心功能</h3>
                        <p>简化学习、快速提分<br>视觉互动、游戏辅助</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-users text-3xl text-green-400 mb-4"></i>
                        <h3>商业模式</h3>
                        <p>在线教育+轻创业<br>加盟线下推广</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第3页：功能特点 -->
        <div class="slide" :class="{ 'active': currentSlide === 2 }">
            <div class="slide-content glass">
                <h2><i class="fas fa-star mr-4"></i>核心功能特点</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <i class="fas fa-graduation-cap text-4xl text-blue-400 mb-4"></i>
                        <h3>简化学习</h3>
                        <p>智能化学习路径，降低学习门槛，让英语学习变得简单高效</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-chart-line text-4xl text-green-400 mb-4"></i>
                        <h3>快速提分</h3>
                        <p>科学的学习方法，针对性训练，快速提升英语成绩</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-eye text-4xl text-purple-400 mb-4"></i>
                        <h3>视觉互动</h3>
                        <p>丰富的视觉元素，互动式学习体验，提高学习兴趣</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-gamepad text-4xl text-yellow-400 mb-4"></i>
                        <h3>游戏辅助</h3>
                        <p>游戏化学习模式，寓教于乐，让孩子爱上英语学习</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-brain text-4xl text-red-400 mb-4"></i>
                        <h3>超脑记忆</h3>
                        <p>科学记忆方法，提高记忆效率，单词记忆更牢固</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第4页：目标客群 -->
        <div class="slide" :class="{ 'active': currentSlide === 3 }">
            <div class="slide-content glass">
                <h2><i class="fas fa-bullseye mr-4"></i>目标客群分析</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
                    <div class="feature-card">
                        <i class="fas fa-user-friends text-4xl text-pink-400 mb-4"></i>
                        <h3>C端客群</h3>
                        <div class="text-left mt-4">
                            <p><span class="highlight">主要群体：</span>小学初中家长</p>
                            <p><span class="highlight">性别分布：</span>女性为主（占70%）</p>
                            <p><span class="highlight">年龄段：</span>35岁~50岁</p>
                            <p><span class="highlight">特征：</span>关注孩子教育，有一定经济实力</p>
                        </div>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-building text-4xl text-blue-400 mb-4"></i>
                        <h3>B端客群</h3>
                        <div class="text-left mt-4">
                            <p><span class="highlight">教培机构：</span>负责人、校长</p>
                            <p><span class="highlight">小型创业者：</span>寻找教育项目</p>
                            <p><span class="highlight">有闲资金家长：</span>缺项目有资金</p>
                            <p><span class="highlight">特征：</span>寻求教育行业投资机会</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第5页：团队架构 -->
        <div class="slide" :class="{ 'active': currentSlide === 4 }">
            <div class="slide-content glass">
                <h2><i class="fas fa-sitemap mr-4"></i>团队架构</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <i class="fas fa-crown text-4xl text-yellow-400 mb-4"></i>
                        <h3>CEO</h3>
                        <p>兼营销总监<br>统筹全局，制定战略</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-cogs text-4xl text-blue-400 mb-4"></i>
                        <h3>产品部</h3>
                        <p>教学产品总监<br>综合设计师</p>
                        <small>专注产品、教学、服务</small>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-server text-4xl text-green-400 mb-4"></i>
                        <h3>运营中心</h3>
                        <p>加盟事业部经理<br>运营专员、客服</p>
                        <small>系统运行、客户服务、加盟洽谈</small>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-bullhorn text-4xl text-red-400 mb-4"></i>
                        <h3>营销部</h3>
                        <p>营销总监、AI技术专员<br>内容生产、分发、成交</p>
                        <small>获客引流、多平台抓潜、首轮成交</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第6页：传统获客成本 -->
        <div class="slide" :class="{ 'active': currentSlide === 5 }">
            <div class="slide-content glass">
                <h2><i class="fas fa-chart-bar mr-4"></i>传统渠道获客成本</h2>
                <div class="data-table">
                    <div class="data-row">
                        <span class="font-bold text-blue-400">渠道</span>
                        <span class="font-bold text-blue-400">获客成本</span>
                    </div>
                    <div class="data-row">
                        <span><i class="fab fa-tiktok mr-2"></i>抖音信息流</span>
                        <span class="highlight">300-500元</span>
                    </div>
                    <div class="data-row">
                        <span><i class="fab fa-instagram mr-2"></i>小红书种草</span>
                        <span class="highlight">400-600元</span>
                    </div>
                    <div class="data-row">
                        <span><i class="fab fa-weixin mr-2"></i>微信朋友圈</span>
                        <span class="highlight">500-800元</span>
                    </div>
                    <div class="data-row">
                        <span><i class="fab fa-google mr-2"></i>百度搜索</span>
                        <span class="highlight">600-1000元</span>
                    </div>
                </div>
                <p class="mt-6 text-xl">
                    <span class="highlight">传统获客成本过高，急需创新营销模式！</span>
                </p>
            </div>
        </div>

        <!-- 第7页：营销核心理念 -->
        <div class="slide" :class="{ 'active': currentSlide === 6 }">
            <div class="slide-content glass">
                <h2><i class="fas fa-lightbulb mr-4"></i>营销核心理念</h2>
                <div class="text-center mb-8">
                    <p class="text-2xl mb-6">
                        <span class="highlight">项目存亡的核心在于营销</span>
                    </p>
                    <p class="text-xl mb-4">营销的核心在于获客与成交率</p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="feature-card">
                        <i class="fas fa-users text-4xl text-blue-400 mb-4"></i>
                        <h3>获客的核心</h3>
                        <p>获客方式的独特性</p>
                        <p>获客成本的极致降低</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-handshake text-4xl text-green-400 mb-4"></i>
                        <h3>成交率的核心</h3>
                        <p>客户分类精准</p>
                        <p>需求深度捕捉</p>
                        <p>服务细腻程度</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第8页：极致低成本获客方式1 -->
        <div class="slide" :class="{ 'active': currentSlide === 7 }">
            <div class="slide-content glass">
                <h2><i class="fas fa-mobile-alt mr-4"></i>低成本获客方式一</h2>
                <div class="feature-card mb-6">
                    <h3 class="text-2xl mb-4">APP使用分享</h3>
                    <p class="text-lg mb-4"><span class="highlight">获客成本：低于10元</span></p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="feature-card">
                        <i class="fas fa-video text-3xl text-blue-400 mb-4"></i>
                        <h3>核心策略</h3>
                        <p>从用户视角拍摄APP功能展示视频</p>
                        <p>多平台发布，引导搜索下载</p>
                        <p>搭配惊讶话术和使用前后对比</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-exclamation-triangle text-3xl text-yellow-400 mb-4"></i>
                        <h3>风险与优势</h3>
                        <p><span class="text-red-400">风险：</span>容易封号，制作成本高</p>
                        <p><span class="text-green-400">优势：</span>直接有效，转化率高</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第9页：极致低成本获客方式2 -->
        <div class="slide" :class="{ 'active': currentSlide === 8 }">
            <div class="slide-content glass">
                <h2><i class="fas fa-gift mr-4"></i>低成本获客方式二</h2>
                <div class="feature-card mb-6">
                    <h3 class="text-2xl mb-4">公域赠品获客</h3>
                    <p class="text-lg mb-4"><span class="highlight">获客成本：低于10元</span></p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="feature-card">
                        <i class="fas fa-book text-3xl text-purple-400 mb-4"></i>
                        <h3>核心思路</h3>
                        <p>制作内容，赠送英语绘本和动画片</p>
                        <p>加微信就送，设计后续跟进流程</p>
                        <p>引导注册APP，储备私域</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-shield-alt text-3xl text-green-400 mb-4"></i>
                        <h3>关键要点</h3>
                        <p>✅ 英语绘本与动画片材料获取</p>
                        <p>✅ 批量视频剪辑技术</p>
                        <p>✅ 公域引导微信不封号</p>
                        <p>✅ 注册APP话术设计</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第10页：极致低成本获客方式3 -->
        <div class="slide" :class="{ 'active': currentSlide === 9 }">
            <div class="slide-content glass">
                <h2><i class="fab fa-weixin mr-4"></i>低成本获客方式三</h2>
                <div class="feature-card mb-6">
                    <h3 class="text-2xl mb-4">家长微信群裂变</h3>
                    <p class="text-lg mb-4"><span class="highlight">获客成本：低于10元</span></p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="feature-card">
                        <i class="fas fa-share-alt text-3xl text-blue-400 mb-4"></i>
                        <h3>裂变策略</h3>
                        <p>在家长群赠送各种需要的资料</p>
                        <p>引导家长加自己，引导注册</p>
                        <p>群生人，人生群，无限裂变</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-comments text-3xl text-yellow-400 mb-4"></i>
                        <h3>成功关键</h3>
                        <p>客户精准，转化率高</p>
                        <p>需要风骚的话术设计</p>
                        <p>人性心理捕捉，让家长愿意拉群</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第11页：其他获客方式 -->
        <div class="slide" :class="{ 'active': currentSlide === 10 }">
            <div class="slide-content glass">
                <h2><i class="fas fa-rocket mr-4"></i>其他获客方式</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <i class="fas fa-medal text-3xl text-yellow-400 mb-4"></i>
                        <h3>会员邀请奖励</h3>
                        <p>APP会员等级设计和奖励制度</p>
                        <p>鼓励会员宣传APP，邀请有奖励</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-map-marker-alt text-3xl text-red-400 mb-4"></i>
                        <h3>线下宣传单</h3>
                        <p>直接去学校给家长发宣传单</p>
                        <p>送礼品、送资料，引导注册</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-crosshairs text-3xl text-purple-400 mb-4"></i>
                        <h3>抓精准客户</h3>
                        <p>根据35-50岁有孩子女性痛点</p>
                        <p>制作内容获取，适合长期运营</p>
                    </div>
                </div>
                <p class="mt-8 text-xl">
                    <span class="highlight">方式不在多，能带团队打穿一个就很厉害！</span>
                </p>
            </div>
        </div>

        <!-- 第12页：初期目标 -->
        <div class="slide" :class="{ 'active': currentSlide === 11 }">
            <div class="slide-content glass">
                <h2><i class="fas fa-target mr-4"></i>建议的初期目标</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <i class="fas fa-users text-4xl text-blue-400 mb-4"></i>
                        <h3>用户注册量</h3>
                        <div class="text-left">
                            <p>第一阶段：<span class="highlight">300人</span></p>
                            <p>第二阶段：<span class="highlight">1,000人</span></p>
                            <p>第三阶段：<span class="highlight">5,000人</span></p>
                            <p>第四阶段：<span class="highlight">10,000人</span></p>
                            <p>终极目标：<span class="highlight">10万人</span></p>
                        </div>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-dollar-sign text-4xl text-green-400 mb-4"></i>
                        <h3>经营目标</h3>
                        <p>付费用户转化率目标</p>
                        <p>加盟商发展目标</p>
                        <p>收入增长目标</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-building text-4xl text-purple-400 mb-4"></i>
                        <h3>团队建设</h3>
                        <p>团队基本架构成型</p>
                        <p>核心岗位人员到位</p>
                        <p>工作流程标准化</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第13页：营销系统设计理念 -->
        <div class="slide" :class="{ 'active': currentSlide === 12 }">
            <div class="slide-content glass">
                <h2><i class="fas fa-cogs mr-4"></i>优质营销系统4大核心</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <i class="fas fa-bullseye text-4xl text-blue-400 mb-4"></i>
                        <h3>客户精准</h3>
                        <p>能捕捉到潜在客户</p>
                        <p>每个客户都有后续成交可能</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-coins text-4xl text-green-400 mb-4"></i>
                        <h3>获客成本低</h3>
                        <p>传统平台获客成本百元以上</p>
                        <p>营销技术降到低于10元</p>
                        <p>中间差值都是利润</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-copy text-4xl text-purple-400 mb-4"></i>
                        <h3>团队复制</h3>
                        <p>工作方式简单可复制</p>
                        <p>系统化工作流程和标准</p>
                        <p>新成员快速成长</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-chart-line text-4xl text-yellow-400 mb-4"></i>
                        <h3>转化率高</h3>
                        <p>清晰细腻的话术设计</p>
                        <p>各个细节的衔接设计</p>
                        <p>避免努力却没结果</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第14页：客户留存与转介绍 -->
        <div class="slide" :class="{ 'active': currentSlide === 13 }">
            <div class="slide-content glass">
                <h2><i class="fas fa-heart mr-4"></i>客户留存与转介绍</h2>
                <div class="text-center mb-8">
                    <p class="text-2xl mb-6">
                        <span class="highlight">特别重要的关键点</span>
                    </p>
                </div>
                <div class="feature-card mb-6">
                    <i class="fas fa-exclamation-circle text-4xl text-red-400 mb-4"></i>
                    <h3>核心挑战</h3>
                    <p class="text-lg">需要强大的产品服务设计与运营，多部门配合</p>
                    <p class="text-lg">否则客户来了也会大量流失，无以为继</p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="feature-card">
                        <i class="fas fa-star text-3xl text-yellow-400 mb-4"></i>
                        <h3>留存策略</h3>
                        <p>优质的产品体验</p>
                        <p>持续的价值输出</p>
                        <p>贴心的客户服务</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-share text-3xl text-blue-400 mb-4"></i>
                        <h3>转介绍机制</h3>
                        <p>满意客户主动推荐</p>
                        <p>口碑传播效应</p>
                        <p>降低获客成本</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第15页：市场机会分析 -->
        <div class="slide" :class="{ 'active': currentSlide === 14 }">
            <div class="slide-content glass">
                <h2><i class="fas fa-chart-pie mr-4"></i>市场机会分析</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="feature-card">
                        <i class="fas fa-globe text-4xl text-blue-400 mb-4"></i>
                        <h3>市场规模</h3>
                        <p>K12英语教育市场：<span class="highlight">206亿元</span></p>
                        <p>目标客群规模：<span class="highlight">1,150万人</span></p>
                        <p>细分市场价值：<span class="highlight">138亿元</span></p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-trophy text-4xl text-yellow-400 mb-4"></i>
                        <h3>竞争优势</h3>
                        <p>35-55岁家长群体是蓝海市场</p>
                        <p>现有竞品专注K12学生</p>
                        <p>差异化定位明显</p>
                    </div>
                </div>
                <div class="feature-card mt-6">
                    <i class="fas fa-lightbulb text-4xl text-green-400 mb-4"></i>
                    <h3>核心洞察</h3>
                    <p class="text-lg">当前英语学习APP主要服务20-29岁用户，35-55岁家长群体被严重忽视，存在巨大的市场空白！</p>
                </div>
            </div>
        </div>

        <!-- 第16页：竞品分析 -->
        <div class="slide" :class="{ 'active': currentSlide === 15 }">
            <div class="slide-content glass">
                <h2><i class="fas fa-chess mr-4"></i>主要竞品分析</h2>
                <div class="data-table">
                    <div class="data-row">
                        <span class="font-bold text-blue-400">竞品</span>
                        <span class="font-bold text-blue-400">下载量</span>
                        <span class="font-bold text-blue-400">主要用户</span>
                    </div>
                    <div class="data-row">
                        <span>百词斩</span>
                        <span class="highlight">6.03亿</span>
                        <span>在校大学生</span>
                    </div>
                    <div class="data-row">
                        <span>扇贝单词</span>
                        <span class="highlight">1.51亿</span>
                        <span>年轻白领</span>
                    </div>
                    <div class="data-row">
                        <span>墨墨背单词</span>
                        <span class="highlight">1.03亿</span>
                        <span>应试用户</span>
                    </div>
                    <div class="data-row">
                        <span>李校来了</span>
                        <span class="highlight">新兴平台</span>
                        <span>K12学生</span>
                    </div>
                </div>
                <p class="mt-6 text-xl">
                    <span class="highlight">所有竞品都专注年轻用户，家长群体市场空白！</span>
                </p>
            </div>
        </div>

        <!-- 第17页：投资回报预测 -->
        <div class="slide" :class="{ 'active': currentSlide === 16 }">
            <div class="slide-content glass">
                <h2><i class="fas fa-calculator mr-4"></i>投资回报预测</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <i class="fas fa-money-bill-wave text-4xl text-green-400 mb-4"></i>
                        <h3>收入预测</h3>
                        <p>目标用户：<span class="highlight">1,150万人</span></p>
                        <p>付费转化率：<span class="highlight">15%</span></p>
                        <p>年均付费：<span class="highlight">800元</span></p>
                        <p>年收入潜力：<span class="highlight">138亿元</span></p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-chart-line text-4xl text-blue-400 mb-4"></i>
                        <h3>成本优势</h3>
                        <p>传统获客：<span class="text-red-400">300-1000元</span></p>
                        <p>创新获客：<span class="highlight">低于10元</span></p>
                        <p>成本节省：<span class="highlight">95%以上</span></p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-rocket text-4xl text-purple-400 mb-4"></i>
                        <h3>增长预期</h3>
                        <p>第一年：基础用户积累</p>
                        <p>第二年：快速增长期</p>
                        <p>第三年：规模化盈利</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第18页：结束页 -->
        <div class="slide" :class="{ 'active': currentSlide === 17 }">
            <div class="slide-content glass glow">
                <div class="mb-8">
                    <img src="https://www.diwangzhidao.com/logo.png" alt="项老师logo" class="w-24 h-24 mx-auto mb-6 animate__animated animate__pulse animate__infinite">
                </div>
                <h1 class="animate__animated animate__fadeInDown">谢谢观看</h1>
                <h2 class="animate__animated animate__fadeInUp animate__delay-1s">Thank You</h2>
                <div class="mt-8 animate__animated animate__fadeIn animate__delay-2s">
                    <p class="text-xl mb-4">
                        <span class="highlight">项老师AI工作室</span>
                    </p>
                    <p class="mb-2">定制超级总裁助理 · AI应用培训 · 企业AI自动化转型</p>
                    <p class="text-sm opacity-75">© 2025 项老师AI工作室 | 传统企业AI自动化转型专家</p>
                </div>
                <div class="mt-8 animate__animated animate__fadeIn animate__delay-3s">
                    <p class="text-lg">
                        <i class="fas fa-rocket mr-2"></i>
                        让我们一起开启英语教育的新时代！
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Alpine.js 数据
        function slideShow() {
            return {
                currentSlide: 0,
                totalSlides: 18,
                slides: Array.from({length: 18}, (_, i) => i),

                nextSlide() {
                    if (this.currentSlide < this.totalSlides - 1) {
                        this.currentSlide++;
                    }
                },

                prevSlide() {
                    if (this.currentSlide > 0) {
                        this.currentSlide--;
                    }
                },

                goToSlide(index) {
                    this.currentSlide = index;
                }
            }
        }

        // 创建粒子背景
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            const app = Alpine.store || window.Alpine;
            if (e.key === 'ArrowRight' || e.key === ' ') {
                e.preventDefault();
                // 触发下一页
                document.querySelector('.nav-btn.next')?.click();
            } else if (e.key === 'ArrowLeft') {
                e.preventDefault();
                // 触发上一页
                document.querySelector('.nav-btn.prev')?.click();
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
        });
    </script>
</body>
</html>
